{"name": "Question", "type": "object", "properties": {"title": {"type": "string", "description": "Question text"}, "category": {"type": "string", "enum": ["general", "science", "technology", "history", "sports", "entertainment"], "description": "Question category"}, "difficulty": {"type": "string", "enum": ["easy", "medium", "hard"], "description": "Question difficulty level"}, "options": {"type": "array", "items": {"type": "string"}, "description": "Multiple choice options"}, "correct_answer": {"type": "number", "description": "Index of correct answer (0-based)"}, "explanation": {"type": "string", "description": "Explanation of the correct answer"}, "is_hot": {"type": "boolean", "default": false, "description": "Whether this is a hot question for today"}, "hot_date": {"type": "string", "format": "date", "description": "Date when this was/is a hot question"}}, "required": ["title", "category", "difficulty", "options", "correct_answer"]}