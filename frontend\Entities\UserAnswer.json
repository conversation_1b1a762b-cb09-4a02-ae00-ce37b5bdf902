{"name": "UserAnswer", "type": "object", "properties": {"user_id": {"type": "string", "description": "ID of the user who answered"}, "question_id": {"type": "string", "description": "ID of the question answered"}, "selected_answer": {"type": "number", "description": "Index of selected answer (0-based)"}, "is_correct": {"type": "boolean", "description": "Whether the answer was correct"}, "points_earned": {"type": "number", "description": "Points earned from this answer"}, "is_hot_question": {"type": "boolean", "default": false, "description": "Whether this was a hot question"}}, "required": ["user_id", "question_id", "selected_answer", "is_correct", "points_earned"]}